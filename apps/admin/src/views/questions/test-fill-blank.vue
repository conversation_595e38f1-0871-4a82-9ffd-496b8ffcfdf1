<template>
  <div class="p-6">
    <h2 class="text-xl font-bold mb-4">填空题中文括号检测测试</h2>
    
    <!-- 测试输入区域 -->
    <div class="mb-6">
      <NFormItem label="测试题干">
        <NInput
          v-model:value="testTitle"
          type="textarea"
          placeholder="请输入包含中文括号（）的填空题题干进行测试"
          :autosize="{ minRows: 3, maxRows: 6 }"
        />
      </NFormItem>
    </div>

    <!-- 检测结果显示 -->
    <div v-if="fillBlankData.chineseBrackets > 0" class="mb-6">
      <NAlert type="info" :show-icon="false">
        <template #header>
          <div class="flex items-center gap-2">
            <NIcon size="16">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="currentColor"/>
              </svg>
            </NIcon>
            <span class="font-medium">检测到 {{ fillBlankData.chineseBrackets }} 个填空</span>
          </div>
        </template>
        <div class="space-y-2">
          <div v-for="(bracket, index) in fillBlankData.brackets" :key="index" class="flex items-center gap-2 text-sm">
            <NBadge :value="index + 1" type="info" />
            <span class="text-gray-600">填空内容:</span>
            <NTag type="primary" size="small">
              {{ bracket.content || '空白' }}
            </NTag>
          </div>
        </div>
      </NAlert>
    </div>

    <!-- 示例题干 -->
    <div class="mb-6">
      <h3 class="text-lg font-semibold mb-2">示例题干</h3>
      <div class="space-y-2">
        <NButton 
          v-for="example in examples" 
          :key="example.title"
          type="tertiary" 
          size="small"
          @click="testTitle = example.title"
        >
          {{ example.description }}
        </NButton>
      </div>
    </div>

    <!-- 原始数据显示 -->
    <div class="bg-gray-50 p-4 rounded">
      <h3 class="text-lg font-semibold mb-2">检测数据</h3>
      <pre class="text-sm">{{ JSON.stringify(fillBlankData, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useDebounceFn } from '@vueuse/core'

defineOptions({
  name: 'TestFillBlank'
})

// 测试题干
const testTitle = ref('')

// 中文括号检测数据
const fillBlankData = ref({
  chineseBrackets: 0,
  brackets: [] as Array<{ index: number, content: string }>
})

// 示例题干
const examples = [
  {
    title: '中国的首都是（北京），最大的城市是（上海）。',
    description: '两个填空示例'
  },
  {
    title: '数学公式：（a+b）²=（a²+2ab+b²）',
    description: '数学公式示例'
  },
  {
    title: '请填写：今天是（），明天是（星期二），后天是（）。',
    description: '混合填空示例'
  },
  {
    title: '这是一个没有填空的普通句子。',
    description: '无填空示例'
  }
]

// 检测中文括号的函数
function detectChineseBrackets(title: string) {
  if (!title) {
    fillBlankData.value = {
      chineseBrackets: 0,
      brackets: []
    }
    return
  }

  // 正则匹配中文括号及其内容
  const chineseBracketRegex = /（([^）]*)）/g
  const brackets: Array<{ index: number, content: string }> = []
  let index = 0

  // 使用 matchAll 替代 while 循环避免 ESLint 警告
  const matches = Array.from(title.matchAll(chineseBracketRegex))

  for (const match of matches) {
    brackets.push({
      index: index++,
      content: match[1] || '' // 括号内的内容
    })
  }

  fillBlankData.value = {
    chineseBrackets: brackets.length,
    brackets
  }

  // 开发环境下打印检测结果
  if (import.meta.env.DEV) {
    // eslint-disable-next-line no-console
    console.log('填空题中文括号检测结果:', {
      总数: fillBlankData.value.chineseBrackets,
      详细信息: fillBlankData.value.brackets,
      原始题干: title
    })
  }
}

// 防抖的中文括号检测函数
const debouncedDetectBrackets = useDebounceFn(detectChineseBrackets, 500)

// 监听题干变化
watch(() => testTitle.value, (newTitle) => {
  if (newTitle) {
    debouncedDetectBrackets(newTitle)
  } else {
    fillBlankData.value = {
      chineseBrackets: 0,
      brackets: []
    }
  }
}, { immediate: true })
</script>
