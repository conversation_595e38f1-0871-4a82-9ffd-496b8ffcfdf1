<script setup lang="ts">
import type { TransformToVoQuestionData } from '@sa/utils'
import { parseCorrectAnswer } from '@sa/utils'
import { QuestionTypeId } from '@sa/enum'
import CKEditor from '@sa/components/common/ck-editor/index.vue'
import type { FormInst } from 'naive-ui'
import QuestionItemContainer from '@sa/components/common/questions/question-item-container.vue'
import { useDebounceFn } from '@vueuse/core'

defineOptions({
  name: 'EditQuestionDrawer',
})

// 定义props
const props = defineProps<{
  question: TransformToVoQuestionData
}>()

// 定义emits
const emit = defineEmits<{
  save: [question: TransformToVoQuestionData]
  cancel: []
}>()

// 抽屉显示状态
const visible = defineModel<boolean>('visible', {
  default: false,
})

// 监听抽屉关闭
function handleClose() {
  visible.value = false
  emit('cancel')
}

const formRef = ref<FormInst | null>(null)

const dynamicForm = reactive({
  name: '',
  hobbies: [{ hobby: '' }],
})
// 创建本地副本用于编辑
const localQuestion = ref<TransformToVoQuestionData>()
// 保存题目
function handleSave() {
  if (!localQuestion.value) {
    window.$message?.error('题目数据不能为空')
    return
  }

  // 基本验证
  if (!localQuestion.value.title?.trim()) {
    window.$message?.error('题干不能为空')
    return
  }

  if (!localQuestion.value.analysis?.trim()) {
    window.$message?.error('答案解析不能为空')
    return
  }

  // 验证选项内容是否为空（适用于有选项的题型）
  if (localQuestion.value.options && localQuestion.value.options.length > 0) {
    const emptyOptions = localQuestion.value.options.filter(option => !option.label?.trim())
    if (emptyOptions.length > 0) {
      window.$message?.error('选项内容不能为空，请完善所有选项')
      return
    }
  }

  // 多选题特殊验证：必须至少有2个正确答案
  if (localQuestion.value.typeId === QuestionTypeId.MULTIPLE_CHOICE) {
    const correctAnswers = parseCorrectAnswer(localQuestion.value.correctAnswer)
    if (correctAnswers.length < 2) {
      window.$message?.error('多选题必须至少设置2个正确答案')
      return
    }
  }

  // 发送编辑后的题目数据
  emit('save', localQuestion.value)
  visible.value = false
  window.$message?.success('题目保存成功')
}
// 添加选项功能
function handleAddOption() {
  if (!localQuestion.value || !localQuestion.value.options) {
    return
  }

  // 获取下一个选项标识符
  const nextOptionValue = getNextOptionValue(localQuestion.value.options)

  // 添加新选项
  const newOption = {
    label: '',
    value: nextOptionValue,
  }

  localQuestion.value.options.push(newOption)
}

// 获取下一个选项标识符（A, B, C, D, E...）
function getNextOptionValue(options: Array<{ label: string, value: string }>): string {
  const existingValues = options.map(option => option.value).sort()
  const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'

  for (let i = 0; i < alphabet.length; i++) {
    const letter = alphabet[i]
    if (!existingValues.includes(letter)) {
      return letter
    }
  }

  // 如果超过26个选项，使用数字
  return String(options.length + 1)
}

// 中文括号检测数据
const fillBlankData = ref({
  chineseBrackets: 0,
  brackets: [] as Array<{ index: number, content: string }>,
})

// 检测中文括号的函数
function detectChineseBrackets(title: string) {
  if (!title) {
    fillBlankData.value = {
      chineseBrackets: 0,
      brackets: [],
    }
    return
  }

  // 正则匹配中文括号及其内容
  const chineseBracketRegex = /（([^）]*)）/g
  const brackets: Array<{ index: number, content: string }> = []
  let index = 0

  // 使用 matchAll 替代 while 循环避免 ESLint 警告
  const matches = Array.from(title.matchAll(chineseBracketRegex))

  for (const match of matches) {
    brackets.push({
      index: index++,
      content: match[1] || '', // 括号内的内容
    })
  }

  fillBlankData.value = {
    chineseBrackets: brackets.length,
    brackets,
  }

  // 开发环境下打印检测结果
  if (import.meta.env.DEV) {
    // eslint-disable-next-line no-console
    console.log('填空题中文括号检测结果:', {
      总数: fillBlankData.value.chineseBrackets,
      详细信息: fillBlankData.value.brackets,
      原始题干: title,
    })
  }
}

// 防抖的中文括号检测函数
const debouncedDetectBrackets = useDebounceFn(detectChineseBrackets, 500)

// 监听填空题题干变化
watch(() => localQuestion.value?.title, (newTitle) => {
  // 只在填空题场景下执行检测
  if (localQuestion.value?.typeId === QuestionTypeId.FILL_BLANK && newTitle) {
    debouncedDetectBrackets(newTitle)
  }
}, { immediate: true })

// 监听 props.question 变化，同步到本地副本
watch(() => props.question, (newQuestion) => {
  if (newQuestion) {
    localQuestion.value = JSON.parse(JSON.stringify(newQuestion))

    // 如果是填空题，立即检测中文括号
    if (newQuestion.typeId === QuestionTypeId.FILL_BLANK && newQuestion.title) {
      detectChineseBrackets(newQuestion.title)
    }
  }
}, { immediate: true, deep: true })
</script>

<template>
  <NDrawer v-model:show="visible" :width="800" placement="right" :auto-focus="false" :trap-focus="false">
    <NDrawerContent :title="`编辑-${localQuestion!.typeText}`" closable :native-scrollbar="false" @close="handleClose">
      <NForm ref="formRef" :model="dynamicForm" label-placement="left" label-width="70px">
        <NFormItem label="题干" :rules="[{ required: true, message: '请输入姓名' }]">
          <CKEditor v-model:editor-value="localQuestion!.title" />
        </NFormItem>

        <!-- 填空题中文括号检测结果显示 -->
        <div v-if="localQuestion?.typeId === QuestionTypeId.FILL_BLANK && fillBlankData.chineseBrackets > 0" class="mb-4">
          <NAlert type="info" :show-icon="false">
            <template #header>
              <div class="flex items-center gap-2">
                <NIcon size="16">
                  <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="currentColor" />
                  </svg>
                </NIcon>
                <span class="font-medium">检测到 {{ fillBlankData.chineseBrackets }} 个填空</span>
              </div>
            </template>
            <div class="space-y-2">
              <div v-for="(bracket, index) in fillBlankData.brackets" :key="index" class="flex items-center gap-2 text-sm">
                <NBadge :value="index + 1" type="info" />
                <span class="text-gray-600">填空内容:</span>
                <NTag type="primary" size="small">
                  {{ bracket.content || '空白' }}
                </NTag>
              </div>
            </div>
          </NAlert>
        </div>
        <QuestionItemContainer v-model:item-info="localQuestion" type="edit" :show-question-stem="false" />

        <!-- 添加选项按钮 -->
        <div v-if="localQuestion && localQuestion.options" class="mb-4">
          <NButton
            type="primary"
            dashed
            class="w-full"
            @click="handleAddOption"
          >
            <template #icon>
              <NIcon>
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 5V19M5 12H19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
              </NIcon>
            </template>
            添加选项
          </NButton>
        </div>

        <NFormItem label="答案解析" :rules="[{ required: true, message: '请输入答案解析' }]">
          <CKEditor v-model:editor-value="localQuestion!.analysis" />
        </NFormItem>
      </NForm>

      <!-- 抽屉底部操作按钮 -->
      <template #footer>
        <div class="flex justify-end gap-3">
          <NButton @click="handleClose">
            取消
          </NButton>
          <NButton type="primary" @click="handleSave">
            保存
          </NButton>
        </div>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>
