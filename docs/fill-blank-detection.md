# 填空题中文括号检测功能

## 功能概述

在填空题编辑场景下，系统会自动检测题干中的中文括号（）并提取相关信息，帮助用户了解填空题的结构。

## 功能特性

### 1. 防抖监听
- 使用 `useDebounceFn` 实现500ms的防抖延迟
- 避免用户输入过程中频繁触发检测
- 提升用户体验和性能

### 2. 智能检测
- 正则表达式：`/（([^）]*)）/g`
- 自动识别中文括号对：（）
- 提取括号内的内容
- 统计填空数量

### 3. 实时显示
- 在填空题编辑界面显示检测结果
- 展示填空数量和具体内容
- 使用 NAlert 组件美观展示

## 技术实现

### 核心代码结构

```typescript
// 中文括号检测数据
const fillBlankData = ref({
  chineseBrackets: 0,
  brackets: [] as Array<{ index: number, content: string }>
})

// 检测函数
function detectChineseBrackets(title: string) {
  const chineseBracketRegex = /（([^）]*)）/g
  const matches = Array.from(title.matchAll(chineseBracketRegex))
  // ... 处理逻辑
}

// 防抖监听
const debouncedDetectBrackets = useDebounceFn(detectChineseBrackets, 500)

// 监听题干变化
watch(() => localQuestion.value?.title, (newTitle) => {
  if (localQuestion.value?.typeId === QuestionTypeId.FILL_BLANK && newTitle) {
    debouncedDetectBrackets(newTitle)
  }
}, { immediate: true })
```

### 数据结构

```typescript
interface FillBlankData {
  chineseBrackets: number  // 中文括号总数
  brackets: Array<{
    index: number         // 填空序号
    content: string      // 括号内容
  }>
}
```

## 使用场景

### 1. 编辑填空题
- 在题目编辑抽屉中自动检测
- 仅在题型为填空题时激活
- 实时更新检测结果

### 2. 检测结果展示
- 显示填空总数
- 列出每个填空的内容
- 空白填空显示为"空白"

## 示例

### 输入题干
```
中国的首都是（北京），最大的城市是（上海）。
```

### 检测结果
```javascript
{
  总数: 2,
  详细信息: [
    { index: 0, content: "北京" },
    { index: 1, content: "上海" }
  ],
  原始题干: "中国的首都是（北京），最大的城市是（上海）。"
}
```

### 界面显示
- 🔍 检测到 2 个填空
  - 1️⃣ 填空内容: 北京
  - 2️⃣ 填空内容: 上海

## 测试页面

创建了专门的测试页面 `test-fill-blank.vue`，包含：
- 输入测试区域
- 实时检测结果显示
- 多个示例题干
- 原始数据展示

## 文件位置

- 主要功能：`apps/admin/src/views/questions/components/right-view/components/edit-question-drawer/index.vue`
- 测试页面：`apps/admin/src/views/questions/test-fill-blank.vue`
- 文档：`docs/fill-blank-detection.md`

## 注意事项

1. 仅在填空题类型（QuestionTypeId.FILL_BLANK = '5'）下激活
2. 使用中文括号（）而非英文括号()
3. 支持嵌套内容和空白填空
4. 开发环境下会在控制台输出详细检测信息
